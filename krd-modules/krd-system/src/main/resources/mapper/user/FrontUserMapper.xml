<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.user.mapper.FrontUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ruoyi.user.domain.entity.FrontUserEntity" id="frontUserMap">
        <result property="id" column="id"/>
        <result property="userIcon" column="user_icon"/>
        <result property="userName" column="user_name"/>
        <result property="userCity" column="user_city"/>
        <result property="userTag" column="user_tag"/>
        <result property="userMobile" column="user_mobile"/>
        <result property="userBirthday" column="user_birthday"/>
        <result property="userAge" column="user_age"/>
        <result property="userSex" column="user_sex"/>
        <result property="userHeight" column="user_height"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="userIp" column="user_ip"/>
        <result property="isDoQuest" column="is_do_quest"/>
        <result property="userWeight" column="user_weight"/>
        <result property="periodCycle" column="period_cycle"/>
        <result property="periodLength" column="period_length"/>
        <result property="nearPeriodDate" column="near_period_date"/>
        <result property="isAnswerHealth" column="is_answer_health"/>
        <result property="isGoPeriod" column="is_go_period"/>
    </resultMap>
    <select id="getUserList" resultType="com.ruoyi.user.domain.vo.UserListVO">
        SELECT
            ur.id AS uid,
            ur.user_name AS uname,
            GROUP_CONCAT(tag.tag_name) AS utags,
            ur.user_mobile AS umobile,
            ur.create_time AS registerTime,
            ur.`status` AS ustatus,
            IF(ur.`status`=0,'正常',IF(ur.`status`=1,'冻结','其他')) AS ustatusDesc
        FROM front_user ur
                 LEFT JOIN front_user_tag ut ON ut.user_id=ur.id
                 LEFT JOIN front_tag tag ON tag.id=ut.tag_id
        <where>
            <if test="ustatus != null and ustatus != ''">
                ur.`status`#{ustatus}
            </if>
            <if test="uname != null and uname != ''">
                ur.`user_name` like ( CONCAT('%',#{uname},'%') )
            </if>
            <if test="searchValue != null and searchValue != ''">
                AND (ur.user_name LIKE CONCAT('%',#{searchValue},'%')
                    OR ur.id LIKE CONCAT('%',#{searchValue},'%')
                    OR ur.user_mobile LIKE CONCAT('%',#{searchValue},'%')
                    )
            </if>
        </where>
        GROUP BY ur.id
    </select>
    <select id="getBalanceTotal" resultType="com.ruoyi.user.domain.dto.BalanceTotalDTO">
        SELECT user_id AS uid,balance AS balanceTotal
        FROM front_balance_info
        WHERE id IN (
        SELECT MAX(id) id
        FROM
        front_balance_info
        WHERE user_id IN (<foreach collection="uidList" item="uid" separator=",">#{uid}</foreach>)
        GROUP BY user_id )
    </select>
    <select id="getUserBoardTop" resultType="com.ruoyi.bullboard.vo.UserBoardTopVo">
        SELECT
            -- 今日新增用户数
            IFNULL(today.todayNewsUserCount, 0) AS todayNewsUserCount,

            -- 昨日新增用户数
            IFNULL(yesterday.yesterdayNewsUserCount, 0) AS yesterdayNewsUserCount,

            -- 相比昨日趋势百分比
            CASE
                WHEN IFNULL(yesterday.yesterdayNewsUserCount, 0) = 0 THEN
                    CASE WHEN IFNULL(today.todayNewsUserCount, 0) > 0 THEN '+100%' ELSE '0%' END
                ELSE
                    CONCAT(
                        CASE WHEN (IFNULL(today.todayNewsUserCount, 0) - IFNULL(yesterday.yesterdayNewsUserCount, 0)) &gt;= 0 THEN '+' ELSE '' END,
                        ROUND(((IFNULL(today.todayNewsUserCount, 0) - IFNULL(yesterday.yesterdayNewsUserCount, 0)) / IFNULL(yesterday.yesterdayNewsUserCount, 0)) * 100, 2),
                        '%'
                    )
            END AS todayNewsUserCountPercent,

            -- 7日新增用户数
            IFNULL(sevenDays.sevenDaysNewsUserCount, 0) AS sevenDaysNewsUserCount,

            -- 相比7日趋势百分比（与前7日对比）
            CASE
                WHEN IFNULL(prevSevenDays.prevSevenDaysNewsUserCount, 0) = 0 THEN
                    CASE WHEN IFNULL(sevenDays.sevenDaysNewsUserCount, 0) > 0 THEN '+100%' ELSE '0%' END
                ELSE
                    CONCAT(
                        CASE WHEN (IFNULL(sevenDays.sevenDaysNewsUserCount, 0) - IFNULL(prevSevenDays.prevSevenDaysNewsUserCount, 0)) &gt;= 0 THEN '+' ELSE '' END,
                        ROUND(((IFNULL(sevenDays.sevenDaysNewsUserCount, 0) - IFNULL(prevSevenDays.prevSevenDaysNewsUserCount, 0)) / IFNULL(prevSevenDays.prevSevenDaysNewsUserCount, 0)) * 100, 2),
                        '%'
                    )
            END AS sevenDaysNewsUserCountPercent,

            -- 30日新增用户数
            IFNULL(thirtyDays.thirtyDaysNewsUserCount, 0) AS thirtyDaysNewsUserCount,

            -- 相比30日趋势百分比（与前30日对比）
            CASE
                WHEN IFNULL(prevThirtyDays.prevThirtyDaysNewsUserCount, 0) = 0 THEN
                    CASE WHEN IFNULL(thirtyDays.thirtyDaysNewsUserCount, 0) > 0 THEN '+100%' ELSE '0%' END
                ELSE
                    CONCAT(
                        CASE WHEN (IFNULL(thirtyDays.thirtyDaysNewsUserCount, 0) - IFNULL(prevThirtyDays.prevThirtyDaysNewsUserCount, 0)) &gt;= 0 THEN '+' ELSE '' END,
                        ROUND(((IFNULL(thirtyDays.thirtyDaysNewsUserCount, 0) - IFNULL(prevThirtyDays.prevThirtyDaysNewsUserCount, 0)) / IFNULL(prevThirtyDays.prevThirtyDaysNewsUserCount, 0)) * 100, 2),
                        '%'
                    )
            END AS thirtyDaysNewsUserCountPercent,

            -- 一年新增用户数
            IFNULL(oneYear.oneYearNewsUserCount, 0) AS oneYearNewsUserCount,

            -- 相比一年趋势百分比（与前一年对比）
            CASE
                WHEN IFNULL(prevOneYear.prevOneYearNewsUserCount, 0) = 0 THEN
                    CASE WHEN IFNULL(oneYear.oneYearNewsUserCount, 0) > 0 THEN '+100%' ELSE '0%' END
                ELSE
                    CONCAT(
                        CASE WHEN (IFNULL(oneYear.oneYearNewsUserCount, 0) - IFNULL(prevOneYear.prevOneYearNewsUserCount, 0)) &gt;= 0 THEN '+' ELSE '' END,
                        ROUND(((IFNULL(oneYear.oneYearNewsUserCount, 0) - IFNULL(prevOneYear.prevOneYearNewsUserCount, 0)) / IFNULL(prevOneYear.prevOneYearNewsUserCount, 0)) * 100, 2),
                        '%'
                    )
            END AS oneYearNewsUserCountPercent

        FROM
            -- 今日新增用户数
            (SELECT COUNT(*) AS todayNewsUserCount
             FROM front_user
             WHERE DATE(create_time) = CURDATE()) today

        CROSS JOIN
            -- 昨日新增用户数
            (SELECT COUNT(*) AS yesterdayNewsUserCount
             FROM front_user
             WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)) yesterday

        CROSS JOIN
            -- 7日新增用户数
            (SELECT COUNT(*) AS sevenDaysNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 6 DAY)) sevenDays

        CROSS JOIN
            -- 前7日新增用户数（用于计算7日趋势）
            (SELECT COUNT(*) AS prevSevenDaysNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 13 DAY)
               AND DATE(create_time) &lt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) prevSevenDays

        CROSS JOIN
            -- 30日新增用户数
            (SELECT COUNT(*) AS thirtyDaysNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 29 DAY)) thirtyDays

        CROSS JOIN
            -- 前30日新增用户数（用于计算30日趋势）
            (SELECT COUNT(*) AS prevThirtyDaysNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 59 DAY)
               AND DATE(create_time) &lt;= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) prevThirtyDays

        CROSS JOIN
            -- 一年新增用户数
            (SELECT COUNT(*) AS oneYearNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 364 DAY)) oneYear

        CROSS JOIN
            -- 前一年新增用户数（用于计算一年趋势）
            (SELECT COUNT(*) AS prevOneYearNewsUserCount
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 729 DAY)
               AND DATE(create_time) &lt;= DATE_SUB(CURDATE(), INTERVAL 365 DAY)) prevOneYear
    </select>
    <select id="getUserDataLine" resultType="com.ruoyi.bullboard.vo.UserDataLineVo">
        WITH RECURSIVE date_range AS (
            -- 生成指定时间段内的所有日期
            SELECT DATE(#{startDate}) AS date
            UNION ALL
            SELECT DATE_ADD(date, INTERVAL 1 DAY)
            FROM date_range
            WHERE date &lt; DATE(#{endDate})
        ),
        daily_new_users AS (
            -- 每日新增用户数
            SELECT
                DATE(create_time) AS date,
                COUNT(*) AS todayNewUserCount
            FROM front_user
            WHERE DATE(create_time) &gt;= DATE(#{startDate})
              AND DATE(create_time) &lt;= DATE(#{endDate})
            GROUP BY DATE(create_time)
        ),
        daily_active_users AS (
            -- 每日活跃用户数（基于签到记录）
            SELECT
                DATE(check_in_date) AS date,
                COUNT(DISTINCT user_id) AS todayActiveUserCount
            FROM front_sign
            WHERE DATE(check_in_date) &gt;= DATE(#{startDate})
              AND DATE(check_in_date) &lt;= DATE(#{endDate})
            GROUP BY DATE(check_in_date)
        ),
        cumulative_users AS (
            -- 累计用户数（截止到每一天的总用户数）
            SELECT
                dr.date,
                (SELECT COUNT(*)
                 FROM front_user
                 WHERE DATE(create_time) &lt;= dr.date) AS totalUserCount
            FROM date_range dr
        )
        SELECT
            DATE_FORMAT(dr.date, '%Y-%m-%d') AS date,
            IFNULL(dnu.todayNewUserCount, 0) AS todayNewUserCount,
            IFNULL(dau.todayActiveUserCount, 0) AS todayActiveUserCount,
            cu.totalUserCount
        FROM date_range dr
        LEFT JOIN daily_new_users dnu ON dr.date = dnu.date
        LEFT JOIN daily_active_users dau ON dr.date = dau.date
        LEFT JOIN cumulative_users cu ON dr.date = cu.date
        ORDER BY dr.date
    </select>
    <select id="getUserAccount" resultType="com.ruoyi.bullboard.vo.UserAccountVo">
        <choose>
            <!-- type = 0: 新增用户占比（今日新增） -->
            <when test="type == 0">
                SELECT
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (male_count * 100.0 / total_count)
                        END, 2
                    ) AS maleRatio,
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (female_count * 100.0 / total_count)
                        END, 2
                    ) AS femaleRatio
                FROM (
                    SELECT
                        COUNT(*) AS total_count,
                        SUM(CASE WHEN user_sex = 1 THEN 1 ELSE 0 END) AS male_count,
                        SUM(CASE WHEN user_sex = 0 THEN 1 ELSE 0 END) AS female_count
                    FROM front_user
                    WHERE DATE(create_time) = CURDATE()
                ) AS user_stats
            </when>

            <!-- type = 1: 活跃用户占比（基于签到记录） -->
            <when test="type == 1">
                SELECT
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (male_count * 100.0 / total_count)
                        END, 2
                    ) AS maleRatio,
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (female_count * 100.0 / total_count)
                        END, 2
                    ) AS femaleRatio
                FROM (
                    SELECT
                        COUNT(DISTINCT u.id) AS total_count,
                        SUM(CASE WHEN u.user_sex = 1 THEN 1 ELSE 0 END) AS male_count,
                        SUM(CASE WHEN u.user_sex = 0 THEN 1 ELSE 0 END) AS female_count
                    FROM front_user u
                    INNER JOIN front_sign s ON u.id = s.user_id
                    WHERE DATE(s.check_in_date) = CURDATE()
                ) AS active_user_stats
            </when>

            <!-- type = 2: 累计用户占比 -->
            <otherwise>
                SELECT
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (male_count * 100.0 / total_count)
                        END, 2
                    ) AS maleRatio,
                    ROUND(
                        CASE
                            WHEN total_count = 0 THEN 0
                            ELSE (female_count * 100.0 / total_count)
                        END, 2
                    ) AS femaleRatio
                FROM (
                    SELECT
                        COUNT(*) AS total_count,
                        SUM(CASE WHEN user_sex = 1 THEN 1 ELSE 0 END) AS male_count,
                        SUM(CASE WHEN user_sex = 0 THEN 1 ELSE 0 END) AS female_count
                    FROM front_user
                ) AS total_user_stats
            </otherwise>
        </choose>
    </select>

    <select id="getUserAddNewInfo" resultType="com.ruoyi.bullboard.vo.UserAddNewInfo">
        SELECT
            -- 指定时间段内新增用户数
            IFNULL(newUsers.newUser, 0) AS newUser,

            -- 去年同期新增用户数
            IFNULL(lastYearUsers.lastYearUser, 0) AS lastYearUser,

            -- 新增用户同比增长率
            CASE
                WHEN IFNULL(lastYearUsers.lastYearUser, 0) = 0 THEN
                    CASE WHEN IFNULL(newUsers.newUser, 0) &gt; 0 THEN 100.0 ELSE 0.0 END
                ELSE
                    ROUND(((IFNULL(newUsers.newUser, 0) - IFNULL(lastYearUsers.lastYearUser, 0)) / IFNULL(lastYearUsers.lastYearUser, 0)) * 100, 2)
            END AS yearOnYearGrowth,

            -- 指定时间段内活跃用户数（基于签到记录）
            IFNULL(activeUsers.activeUser, 0) AS activeUser,

            -- 去年同期活跃用户数
            IFNULL(lastYearActiveUsers.lastYearActiveUser, 0) AS lastYearActiveUser,

            -- 活跃用户同比增长率
            CASE
                WHEN IFNULL(lastYearActiveUsers.lastYearActiveUser, 0) = 0 THEN
                    CASE WHEN IFNULL(activeUsers.activeUser, 0) &gt; 0 THEN 100.0 ELSE 0.0 END
                ELSE
                    ROUND(((IFNULL(activeUsers.activeUser, 0) - IFNULL(lastYearActiveUsers.lastYearActiveUser, 0)) / IFNULL(lastYearActiveUsers.lastYearActiveUser, 0)) * 100, 2)
            END AS activeGrowth,

            -- 截止到结束日期的累计用户数
            IFNULL(totalUsers.totalUser, 0) AS totalUser,

            -- 去年同期累计用户数
            IFNULL(lastYearTotalUsers.lastYearTotalUser, 0) AS lastYearTotalUser,

            -- 累计用户同比增长率
            CASE
                WHEN IFNULL(lastYearTotalUsers.lastYearTotalUser, 0) = 0 THEN
                    CASE WHEN IFNULL(totalUsers.totalUser, 0) &gt; 0 THEN 100.0 ELSE 0.0 END
                ELSE
                    ROUND(((IFNULL(totalUsers.totalUser, 0) - IFNULL(lastYearTotalUsers.lastYearTotalUser, 0)) / IFNULL(lastYearTotalUsers.lastYearTotalUser, 0)) * 100, 2)
            END AS totalGrowth

        FROM
            -- 指定时间段内新增用户数
            (SELECT COUNT(*) AS newUser
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE(#{startDate})
               AND DATE(create_time) &lt;= DATE(#{endDate})) newUsers

        CROSS JOIN
            -- 去年同期新增用户数
            (SELECT COUNT(*) AS lastYearUser
             FROM front_user
             WHERE DATE(create_time) &gt;= DATE_SUB(DATE(#{startDate}), INTERVAL 1 YEAR)
               AND DATE(create_time) &lt;= DATE_SUB(DATE(#{endDate}), INTERVAL 1 YEAR)) lastYearUsers

        CROSS JOIN
            -- 指定时间段内活跃用户数（基于签到记录）
            (SELECT COUNT(DISTINCT user_id) AS activeUser
             FROM front_sign
             WHERE DATE(check_in_date) &gt;= DATE(#{startDate})
               AND DATE(check_in_date) &lt;= DATE(#{endDate})) activeUsers

        CROSS JOIN
            -- 去年同期活跃用户数
            (SELECT COUNT(DISTINCT user_id) AS lastYearActiveUser
             FROM front_sign
             WHERE DATE(check_in_date) &gt;= DATE_SUB(DATE(#{startDate}), INTERVAL 1 YEAR)
               AND DATE(check_in_date) &lt;= DATE_SUB(DATE(#{endDate}), INTERVAL 1 YEAR)) lastYearActiveUsers

        CROSS JOIN
            -- 截止到结束日期的累计用户数
            (SELECT COUNT(*) AS totalUser
             FROM front_user
             WHERE DATE(create_time) &lt;= DATE(#{endDate})) totalUsers

        CROSS JOIN
            -- 去年同期累计用户数
            (SELECT COUNT(*) AS lastYearTotalUser
             FROM front_user
             WHERE DATE(create_time) &lt;= DATE_SUB(DATE(#{endDate}), INTERVAL 1 YEAR)) lastYearTotalUsers
    </select>
    <select id="getUserDataInfo" resultType="com.ruoyi.bullboard.vo.UserDataInfoVo">
        WITH RECURSIVE date_range AS (
            -- 生成指定时间段内的所有日期
            SELECT DATE(#{startDate}) AS date
            UNION ALL
            SELECT DATE_ADD(date, INTERVAL 1 DAY)
            FROM date_range
            WHERE date &lt; DATE(#{endDate})
        ),
        half_day_periods AS (
            -- 为每个日期生成上午和下午两个时间段
            SELECT
                dr.date,
                'AM' AS period,
                CONCAT(DATE_FORMAT(dr.date, '%Y-%m-%d'), ' 00:00:00') AS period_start,
                CONCAT(DATE_FORMAT(dr.date, '%Y-%m-%d'), ' 11:59:59') AS period_end
            FROM date_range dr
            UNION ALL
            SELECT
                dr.date,
                'PM' AS period,
                CONCAT(DATE_FORMAT(dr.date, '%Y-%m-%d'), ' 12:00:00') AS period_start,
                CONCAT(DATE_FORMAT(dr.date, '%Y-%m-%d'), ' 23:59:59') AS period_end
            FROM date_range dr
        ),
        half_day_user_stats AS (
            -- 统计每个半天的新用户和老用户数据
            SELECT
                hdp.date,
                hdp.period,
                -- 新用户：在该半天时间段内注册的用户
                (SELECT COUNT(*)
                 FROM front_user
                 WHERE create_time &gt;= hdp.period_start
                   AND create_time &lt;= hdp.period_end) AS newUser,
                -- 老用户：在该半天时间段内活跃但注册时间早于该时间段的用户
                (SELECT COUNT(DISTINCT fs.user_id)
                 FROM front_sign fs
                 INNER JOIN front_user fu ON fs.user_id = fu.id
                 WHERE fs.check_in_date &gt;= hdp.period_start
                   AND fs.check_in_date &lt;= hdp.period_end
                   AND fu.create_time &lt; hdp.period_start) AS oldUser
            FROM half_day_periods hdp
        )
        SELECT
            DATE_FORMAT(hus.date, '%Y-%m-%d') AS date,
            hus.period,
            hus.newUser,
            CASE
                WHEN (hus.newUser + hus.oldUser) = 0 THEN '0%'
                ELSE CONCAT(ROUND((hus.newUser * 100.0 / (hus.newUser + hus.oldUser)), 2), '%')
            END AS newUserPercent,
            hus.oldUser,
            CASE
                WHEN (hus.newUser + hus.oldUser) = 0 THEN '0%'
                ELSE CONCAT(ROUND((hus.oldUser * 100.0 / (hus.newUser + hus.oldUser)), 2), '%')
            END AS oldUserPercent
        FROM half_day_user_stats hus
        ORDER BY hus.date, hus.period
    </select>

    <select id="getUserAgeDistribution" resultType="com.ruoyi.bullboard.vo.UserAgeDistributionVo">
        <choose>
            <!-- type = 0: 新增用户年龄分布（今日新增） -->
            <when test="type == 0">
                SELECT
                    CASE
                        WHEN user_age IS NULL OR user_age = 0 THEN '未知'
                        WHEN user_age &lt; 20 THEN '20岁以下'
                        WHEN user_age BETWEEN 20 AND 29 THEN '20-29岁'
                        WHEN user_age BETWEEN 30 AND 39 THEN '30-39岁'
                        WHEN user_age BETWEEN 40 AND 49 THEN '40-49岁'
                        WHEN user_age &gt;= 50 THEN '50岁以上'
                        ELSE '未知'
                    END AS ageRange,
                    COUNT(*) AS userCount,
                    CONCAT(ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM front_user WHERE DATE(create_time) = CURDATE())), 2), '%') AS percentage
                FROM front_user
                WHERE DATE(create_time) = CURDATE()
                GROUP BY
                    CASE
                        WHEN user_age IS NULL OR user_age = 0 THEN '未知'
                        WHEN user_age &lt; 20 THEN '20岁以下'
                        WHEN user_age BETWEEN 20 AND 29 THEN '20-29岁'
                        WHEN user_age BETWEEN 30 AND 39 THEN '30-39岁'
                        WHEN user_age BETWEEN 40 AND 49 THEN '40-49岁'
                        WHEN user_age &gt;= 50 THEN '50岁以上'
                        ELSE '未知'
                    END
                ORDER BY
                    CASE
                        WHEN user_age IS NULL OR user_age = 0 THEN 6
                        WHEN user_age &lt; 20 THEN 1
                        WHEN user_age BETWEEN 20 AND 29 THEN 2
                        WHEN user_age BETWEEN 30 AND 39 THEN 3
                        WHEN user_age BETWEEN 40 AND 49 THEN 4
                        WHEN user_age &gt;= 50 THEN 5
                        ELSE 6
                    END
            </when>

            <!-- type = 1: 活跃用户年龄分布（基于签到记录） -->
            <when test="type == 1">
                SELECT
                    CASE
                        WHEN u.user_age IS NULL OR u.user_age = 0 THEN '未知'
                        WHEN u.user_age &lt; 20 THEN '20岁以下'
                        WHEN u.user_age BETWEEN 20 AND 29 THEN '20-29岁'
                        WHEN u.user_age BETWEEN 30 AND 39 THEN '30-39岁'
                        WHEN u.user_age BETWEEN 40 AND 49 THEN '40-49岁'
                        WHEN u.user_age &gt;= 50 THEN '50岁以上'
                        ELSE '未知'
                    END AS ageRange,
                    COUNT(DISTINCT u.id) AS userCount,
                    CONCAT(ROUND((COUNT(DISTINCT u.id) * 100.0 / (SELECT COUNT(DISTINCT user_id) FROM front_sign WHERE DATE(check_in_date) = CURDATE())), 2), '%') AS percentage
                FROM front_user u
                INNER JOIN front_sign s ON u.id = s.user_id
                WHERE DATE(s.check_in_date) = CURDATE()
                GROUP BY
                    CASE
                        WHEN u.user_age IS NULL OR u.user_age = 0 THEN '未知'
                        WHEN u.user_age &lt; 20 THEN '20岁以下'
                        WHEN u.user_age BETWEEN 20 AND 29 THEN '20-29岁'
                        WHEN u.user_age BETWEEN 30 AND 39 THEN '30-39岁'
                        WHEN u.user_age BETWEEN 40 AND 49 THEN '40-49岁'
                        WHEN u.user_age &gt;= 50 THEN '50岁以上'
                        ELSE '未知'
                    END
                ORDER BY
                    CASE
                        WHEN u.user_age IS NULL OR u.user_age = 0 THEN 6
                        WHEN u.user_age &lt; 20 THEN 1
                        WHEN u.user_age BETWEEN 20 AND 29 THEN 2
                        WHEN u.user_age BETWEEN 30 AND 39 THEN 3
                        WHEN u.user_age BETWEEN 40 AND 49 THEN 4
                        WHEN u.user_age &gt;= 50 THEN 5
                        ELSE 6
                    END
            </when>

            <!-- type = 2: 累计用户年龄分布 -->
            <otherwise>
                SELECT
                    CASE
                        WHEN user_age IS NULL OR user_age = 0 THEN '未知'
                        WHEN user_age &lt; 20 THEN '20岁以下'
                        WHEN user_age BETWEEN 20 AND 29 THEN '20-29岁'
                        WHEN user_age BETWEEN 30 AND 39 THEN '30-39岁'
                        WHEN user_age BETWEEN 40 AND 49 THEN '40-49岁'
                        WHEN user_age &gt;= 50 THEN '50岁以上'
                        ELSE '未知'
                    END AS ageRange,
                    COUNT(*) AS userCount,
                    CONCAT(ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM front_user)), 2), '%') AS percentage
                FROM front_user
                GROUP BY
                    CASE
                        WHEN user_age IS NULL OR user_age = 0 THEN '未知'
                        WHEN user_age &lt; 20 THEN '20岁以下'
                        WHEN user_age BETWEEN 20 AND 29 THEN '20-29岁'
                        WHEN user_age BETWEEN 30 AND 39 THEN '30-39岁'
                        WHEN user_age BETWEEN 40 AND 49 THEN '40-49岁'
                        WHEN user_age &gt;= 50 THEN '50岁以上'
                        ELSE '未知'
                    END
                ORDER BY
                    CASE
                        WHEN user_age IS NULL OR user_age = 0 THEN 6
                        WHEN user_age &lt; 20 THEN 1
                        WHEN user_age BETWEEN 20 AND 29 THEN 2
                        WHEN user_age BETWEEN 30 AND 39 THEN 3
                        WHEN user_age BETWEEN 40 AND 49 THEN 4
                        WHEN user_age &gt;= 50 THEN 5
                        ELSE 6
                    END
            </otherwise>
        </choose>
    </select>

    <select id="getUserRegionDistribution" resultType="com.ruoyi.bullboard.vo.UserRegionDistributionVo">
        <choose>
            <!-- type = 0: 新增用户地域分布（今日新增） -->
            <when test="type == 0">
                SELECT
                    ROW_NUMBER() OVER (ORDER BY COUNT(*) DESC) AS `rank`,
                    CASE
                        WHEN user_city IS NULL OR user_city = '' THEN '未知'
                        ELSE user_city
                    END AS regionName,
                    COUNT(*) AS userCount,
                    CONCAT(ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM front_user WHERE DATE(create_time) = CURDATE())), 2), '%') AS percentage
                FROM front_user
                WHERE DATE(create_time) = CURDATE()
                GROUP BY
                    CASE
                        WHEN user_city IS NULL OR user_city = '' THEN '未知'
                        ELSE user_city
                    END
                ORDER BY userCount DESC
                LIMIT 10
            </when>

            <!-- type = 1: 活跃用户地域分布（基于签到记录） -->
            <when test="type == 1">
                SELECT
                    ROW_NUMBER() OVER (ORDER BY COUNT(DISTINCT u.id) DESC) AS `rank`,
                    CASE
                        WHEN u.user_city IS NULL OR u.user_city = '' THEN '未知'
                        ELSE u.user_city
                    END AS regionName,
                    COUNT(DISTINCT u.id) AS userCount,
                    CONCAT(ROUND((COUNT(DISTINCT u.id) * 100.0 / (SELECT COUNT(DISTINCT user_id) FROM front_sign WHERE DATE(check_in_date) = CURDATE())), 2), '%') AS percentage
                FROM front_user u
                INNER JOIN front_sign s ON u.id = s.user_id
                WHERE DATE(s.check_in_date) = CURDATE()
                GROUP BY
                    CASE
                        WHEN u.user_city IS NULL OR u.user_city = '' THEN '未知'
                        ELSE u.user_city
                    END
                ORDER BY userCount DESC
                LIMIT 10
            </when>

            <!-- type = 2: 累计用户地域分布 -->
            <otherwise>
                SELECT
                    ROW_NUMBER() OVER (ORDER BY COUNT(*) DESC) AS `rank`,
                    CASE
                        WHEN user_city IS NULL OR user_city = '' THEN '未知'
                        ELSE user_city
                    END AS regionName,
                    COUNT(*) AS userCount,
                    CONCAT(ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM front_user)), 2), '%') AS percentage
                FROM front_user
                GROUP BY
                    CASE
                        WHEN user_city IS NULL OR user_city = '' THEN '未知'
                        ELSE user_city
                    END
                ORDER BY userCount DESC
                LIMIT 10
            </otherwise>
        </choose>
    </select>

    <select id="getUserTagDistribution" resultType="com.ruoyi.bullboard.vo.UserTagDistributionVo">
        SELECT
            tag_name AS tagName,
            CONCAT(ROUND((user_count * 100.0 / (SELECT SUM(user_count) FROM front_tag WHERE is_del = 0)), 2), '%') AS percentage
        FROM front_tag
        WHERE is_del = 0
        ORDER BY user_count DESC
    </select>

</mapper>
