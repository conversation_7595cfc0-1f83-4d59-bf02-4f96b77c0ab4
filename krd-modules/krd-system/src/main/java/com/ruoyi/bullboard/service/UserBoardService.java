package com.ruoyi.bullboard.service;

import com.ruoyi.bullboard.vo.*;

import java.util.List;

/**
 * 用户数据看板服务接口
 * <AUTHOR>
 * @date 2025/6/26
 */
public interface UserBoardService {

    /**
     * 获取用户数据看板头部新增信息
     * @return 用户数据看板头部信息
     */
    UserBoardTopVo getUserBoardTop();

    /**
     * 获取用户数据折线图
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 用户数据折线图数据
     */
    List<UserDataLineVo> getUserDataLine(String startDate, String endDate);

    /**
     * 用户右侧用户数据统计信息
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 用户新增统计信息
     */
    UserAddNewInfo getUserAddNewInfo(String startDate, String endDate);

    /**
     * 获取用户占比信息
     * @param type 0-新增用户占比 1-活跃用户占比 2-累计用户占比
     * @return 用户性别占比数据
     */
    UserAccountVo getUserAccount(Integer type);

    /**
     * 获取用户使用数据（按半天统计）
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 用户使用数据，每天包含上午(AM)和下午(PM)两个数据点
     */
    List<UserDataInfoVo> getUserDataInfo(String startDate, String endDate);

    /**
     * 获取用户年龄分布
     * @param type 0-新增用户年龄分布 1-活跃用户年龄分布 2-累计用户年龄分布
     * @return 用户年龄分布数据
     */
    List<UserAgeDistributionVo> getUserAgeDistribution(Integer type);

    /**
     * 获取用户地域分布（TOP 10）
     * @param type 0-新增用户地域分布 1-活跃用户地域分布 2-累计用户地域分布
     * @return 用户地域分布数据，返回TOP 10城市
     */
    List<UserRegionDistributionVo> getUserRegionDistribution(Integer type);

    /**
     * 获取用户标签分布（词云图数据）
     * @return 用户标签分布数据，用于生成词云图
     */
    List<UserTagDistributionVo> getUserTagDistribution();
}
