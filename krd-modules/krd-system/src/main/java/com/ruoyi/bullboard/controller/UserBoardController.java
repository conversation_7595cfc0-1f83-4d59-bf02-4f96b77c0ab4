package com.ruoyi.bullboard.controller;

import com.ruoyi.bullboard.service.UserBoardService;
import com.ruoyi.bullboard.vo.*;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据看板模块---用户数据
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/board/user")
public class UserBoardController extends BaseController
{
    @Autowired
    private UserBoardService userBoardService;

    /**
     * 获取用户数据看板头部新增信息
     * @return 用户数据看板头部信息
     */
    @GetMapping("/getUserBoardTop")
    public R<UserBoardTopVo> getUserBoardTop() {
        UserBoardTopVo userBoardTop = userBoardService.getUserBoardTop();
        return R.ok(userBoardTop);
    }

    /**
     * 获取用户数据折线图
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 用户数据折线图数据
     */
    @GetMapping("/getUserDataLine")
    public R<List<UserDataLineVo>> getUserDataLine(@RequestParam("startDate") String startDate,
                                                   @RequestParam("endDate") String endDate) {
        List<UserDataLineVo> userDataLine = userBoardService.getUserDataLine(startDate, endDate);
        return R.ok(userDataLine);
    }

    /**
     * 用户右侧用户数据统计信息
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 用户新增统计信息
     */
    @GetMapping("/getUserAddNewInfo")
    public R<UserAddNewInfo> getUserAddNewInfo(@RequestParam("startDate") String startDate,
                                               @RequestParam("endDate") String endDate) {
        UserAddNewInfo userAddNewInfo = userBoardService.getUserAddNewInfo(startDate, endDate);
        return R.ok(userAddNewInfo);
    }

    /**
     * 获取用户性别占比
     * @param type 0-新增用户占比 1-活跃用户占比 2-累计用户占比
     * @return 用户性别占比数据
     */
    @GetMapping("/getUserAccount")
    public R<UserAccountVo> getUserAccount(@RequestParam("type") Integer type) {
        UserAccountVo userAccount = userBoardService.getUserAccount(type);
        return R.ok(userAccount);
    }

    /**
     * 获取用户使用数据（按半天统计）
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 用户使用数据，每天包含上午(AM)和下午(PM)两个数据点，统计新用户和老用户数据
     */
    @GetMapping("/getUserDataInfo")
    public R<List<UserDataInfoVo>> getUserDataInfo(@RequestParam("startDate") String startDate,
                                                   @RequestParam("endDate") String endDate) {
        List<UserDataInfoVo> userDataInfo = userBoardService.getUserDataInfo(startDate, endDate);
        return R.ok(userDataInfo);
    }

    /**
     * 获取用户年龄分布
     * @param type 0-新增用户年龄分布 1-活跃用户年龄分布 2-累计用户年龄分布
     * @return 用户年龄分布数据
     */
    @GetMapping("/getUserAgeDistribution")
    public R<List<UserAgeDistributionVo>> getUserAgeDistribution(@RequestParam("type") Integer type) {
        List<UserAgeDistributionVo> ageDistribution = userBoardService.getUserAgeDistribution(type);
        return R.ok(ageDistribution);
    }

    /**
     * 获取用户地域分布（TOP 10）
     * @param type 0-新增用户地域分布 1-活跃用户地域分布 2-累计用户地域分布
     * @return 用户地域分布数据，返回TOP 10城市
     */
    @GetMapping("/getUserRegionDistribution")
    public R<List<UserRegionDistributionVo>> getUserRegionDistribution(@RequestParam("type") Integer type) {
        List<UserRegionDistributionVo> regionDistribution = userBoardService.getUserRegionDistribution(type);
        return R.ok(regionDistribution);
    }

    /**
     * 获取用户标签分布（词云图数据）
     * @return 用户标签分布数据，用于生成词云图
     */
    @GetMapping("/getUserTagDistribution")
    public R<List<UserTagDistributionVo>> getUserTagDistribution() {
        List<UserTagDistributionVo> tagDistribution = userBoardService.getUserTagDistribution();
        return R.ok(tagDistribution);
    }
}
