package com.ruoyi.bullboard.service.impl;

import com.ruoyi.bullboard.service.UserBoardService;
import com.ruoyi.bullboard.vo.*;
import com.ruoyi.user.mapper.FrontUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户数据看板服务实现类
 * @Author: suhai
 * @Date: 2025/6/26
 * @Description: 用户数据看板相关业务逻辑实现
 */
@Service
public class UserBoardServiceImpl implements UserBoardService {

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Override
    public UserBoardTopVo getUserBoardTop() {
        return frontUserMapper.getUserBoardTop();
    }

    @Override
    public List<UserDataLineVo> getUserDataLine(String startDate, String endDate) {
        return frontUserMapper.getUserDataLine(startDate, endDate);
    }

    @Override
    public UserAddNewInfo getUserAddNewInfo(String startDate, String endDate) {
        return frontUserMapper.getUserAddNewInfo(startDate, endDate);
    }

    @Override
    public UserAccountVo getUserAccount(Integer type) {
        return frontUserMapper.getUserAccount(type);
    }

    @Override
    public List<UserDataInfoVo> getUserDataInfo(String startDate, String endDate) {
        return frontUserMapper.getUserDataInfo(startDate, endDate);
    }

    @Override
    public List<UserAgeDistributionVo> getUserAgeDistribution(Integer type) {
        return frontUserMapper.getUserAgeDistribution(type);
    }

    @Override
    public List<UserRegionDistributionVo> getUserRegionDistribution(Integer type) {
        return frontUserMapper.getUserRegionDistribution(type);
    }

    @Override
    public List<UserTagDistributionVo> getUserTagDistribution() {
        return frontUserMapper.getUserTagDistribution();
    }
}
